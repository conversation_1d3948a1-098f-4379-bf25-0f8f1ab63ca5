{"name": "evo-api", "version": "1.2.1", "description": "A REST API to serve the EVO PTT Mongo database to external services", "author": "<PERSON><PERSON><PERSON>", "private": true, "license": "ISC", "scripts": {"prebuild": "rm -rf dist", "build": "cross-env NODE_ENV=production nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "bun prebuild && cross-env NODE_ENV=development nest start --watch", "debug": "cross-env NODE_ENV=development nest start --debug --watch", "prod": "cross-env NODE_ENV=production node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "doctor": "cross-env NODE_ENV=development clinic doctor -- node dist/main", "doctor-asyncdelay": "cross-env NODE_ENV=development clinic bubbleproof -- node dist/main", "doctor-flamegraph": "cross-env NODE_ENV=development clinic flame -- node dist/main", "doctor-heapprof": "cross-env NODE_ENV=development clinic heapprofiler -- node dist/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@fastify/compress": "^8.0.1", "@fastify/helmet": "^13.0.1", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-fastify": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/throttler": "^6.4.0", "cache-manager": "^6.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "country-locator": "^2.1.1", "dayjs": "^1.11.13", "mongoose": "8.15.1", "node-public-ip": "^1.0.2", "passport": "^0.7.0", "passport-headerapikey": "^1.2.2", "reflect-metadata": "^0.2.2", "systeminformation": "^5.27.1"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@types/express": "^5.0.3", "@types/jest": "29.5.14", "@types/node": "^22.15.30", "@types/passport": "^1.0.17", "@types/passport-http": "^0.3.11", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "cross-env": "^7.0.3", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "jest": "29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "29.3.4", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "volta": {"node": "24.1.0", "npm": "11.4.1"}}