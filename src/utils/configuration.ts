export const getConfiguration = () => {
  const isDemo = process.env.DEMO === 'true';
  const environment = process.env.NODE_ENV || 'development';
  const isProduction = environment === 'production';

  let port;

  if (isProduction) {
    port = process.env.PROD_PORT || 3000;
  } else {
    port = process.env.DEV_PORT || 3000;
  }

  const https = process.env.HTTPS === 'true';
  const domain = process.env.DOMAIN || '';
  const ipAddress = process.env.IP_ADDRESS || 'localhost';
  const serverUrl = https ? 'https://' + domain : 'http://' + ipAddress;
  const certificatesPath = https ? '/etc/letsencrypt/live/' + domain : '';

  const dbHost = process.env.DB_HOST;
  const dbUser = process.env.DB_USER;
  const dbPassword = process.env.DB_PASSWORD;
  const dbPort = process.env.DB_PORT;
  const dbName = process.env.DB_NAME;
  const dbAuth = process.env.DB_AUTH;

  let connectionUrl: string = '';
  const dbAuthString = dbAuth?.toLowerCase() === 'true' ? `${dbUser}:${dbPassword}@` : '';

  const mongoOptimizations = [
    'retryWrites=true',
    'w=majority',
    'maxPoolSize=5',
    'minPoolSize=1',
    'maxIdleTimeMS=30000',
    'serverSelectionTimeoutMS=5000',
    'socketTimeoutMS=30000',
    'compressors=zstd',
  ].join('&');

  if (dbHost === 'localhost' || dbHost === '127.0.0.1') {
    connectionUrl = `mongodb://${dbAuthString}127.0.0.1:${dbPort}/${dbName}?${mongoOptimizations}`;
  } else {
    connectionUrl = `mongodb+srv://${dbAuthString}${dbHost}/${dbName}?${mongoOptimizations}`;
  }

  return {
    server: {
      environment,
      isProduction,
      isDemo,
      port,
      domain,
      https,
      ipAddress,
      serverUrl,
      certificatesPath,
      adminKey: process.env.ADMIN_KEY,
    },
    database: {
      host: dbHost,
      user: dbUser,
      password: dbPassword,
      port: dbPort,
      name: dbName,
      connectionUrl,
    },
    mumble: {
      portRangeStart: process.env.MUMBLE_PORT_RANGE_START,
      serverName: process.env.MUMBLE_SERVER_NAME,
      serverPass: process.env.MUMBLE_SERVER_PASS,
    },
  };
};
