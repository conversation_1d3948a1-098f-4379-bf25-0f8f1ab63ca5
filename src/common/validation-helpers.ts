import { ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import { getConfiguration } from '../utils/configuration';

export const strongPasswordPolicy = {
  minLength: 7,
  minLowercase: 1,
  minNumbers: 1,
  minSymbols: 0,
  minUppercase: 0,
};

@ValidatorConstraint({ name: 'isAdminKey' })
export class IsAdminKey implements ValidatorConstraintInterface {
  validate(inputKey: string): boolean {
    const adminKey = getConfiguration()?.server?.adminKey;
    return !!(adminKey && inputKey === adminKey);
  }
}
