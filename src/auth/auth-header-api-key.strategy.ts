import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import Strategy from 'passport-headerapikey';
import { getConfiguration } from '../utils/configuration';

@Injectable()
export class HeaderApiKeyStrategy extends PassportStrategy(Strategy as any, 'api-key') {
  constructor(private readonly configService: ConfigService) {
    super(
      { header: 'x-api-key', prefix: '' },
      true,
      async (apiKey: string, done: (error: Error, data: unknown) => {}) => {
        return this.validate(apiKey, done);
      },
    );
  }

  public validate = (apiKey: string, done: (error: Error | null, data: unknown) => {}) => {
    const adminKey = getConfiguration()?.server?.adminKey;
    if (adminKey && adminKey === apiKey) {
      return done(null, true);
    }
    return done(new UnauthorizedException(), null);
  };
}
