import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, now } from 'mongoose';
import { Company } from '../../companies/schemas/company.schema';

export type UserDocument = User & Document;

@Schema({ timestamps: true, autoCreate: true })
export class User {
  @Prop({ required: true, index: true, unique: true })
  username: string;

  @Prop({ required: true })
  password: string;

  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop({ default: now() })
  createdAt: Date;

  @Prop({ required: true })
  phone: string;

  @Prop({ required: false, default: 'N/A' })
  country: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: Company.name, index: true })
  companyId: string;

  @Prop({ required: false })
  accuracy: number;

  @Prop({ required: false })
  appVersionCode: number;

  @Prop({ required: false })
  appVersionNumber: string;

  @Prop({ required: false })
  batteryLevel: number;

  @Prop({ required: false })
  buildSerial: string;

  @Prop({ required: false })
  deviceExternalIP: string;

  @Prop({ required: false })
  deviceId: string;

  @Prop({ required: false })
  deviceModel: string;

  @Prop({ required: false })
  deviceOSVersion: string;

  @Prop({ required: false })
  latitude: number;

  @Prop({ required: false })
  longitude: number;

  @Prop({ required: false })
  lastRecordTimestamp: number;

  @Prop({ required: false })
  volumeLevel: number;
}

export const UserSchema = SchemaFactory.createForClass(User);

// Compound indexes for better query performance
UserSchema.index({ username: 1, password: 1 });
UserSchema.index({ companyId: 1 });
UserSchema.index({ lastRecordTimestamp: -1 });
