import { Transform } from 'class-transformer';
import { toUpperCase, trim } from '../../common/transform-helpers';
import {
  IsMongoId,
  IsNotEmpty,
  IsPhoneNumber,
  IsString,
  IsStrongPassword,
  MaxLength,
} from 'class-validator';
import { strongPasswordPolicy } from '../../common/validation-helpers';

export class CreateUserDto {
  @Transform(({ value }) => trim(value))
  @IsString()
  @IsNotEmpty()
  username: string;

  @Transform(({ value }) => trim(value))
  @IsStrongPassword(strongPasswordPolicy)
  @IsNotEmpty()
  password: string;

  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @Transform(({ value }) => trim(value))
  @IsPhoneNumber()
  @IsNotEmpty()
  phone: string;

  @Transform(({ value }) => trim(value))
  @Transform(({ value }) => toUpperCase(value))
  @IsString()
  @MaxLength(3)
  @IsNotEmpty()
  country: string;

  @IsString()
  @IsMongoId()
  @IsNotEmpty()
  companyId: string;
}
