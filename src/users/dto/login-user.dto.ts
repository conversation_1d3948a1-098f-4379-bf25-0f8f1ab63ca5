import { IsBoolean, <PERSON><PERSON>ot<PERSON>mpty, <PERSON><PERSON><PERSON>al, IsString, IsStrongPassword } from 'class-validator';
import { strongPasswordPolicy } from '../../common/validation-helpers';
import { Transform } from 'class-transformer';
import { trim } from '../../common/transform-helpers';

export class LoginUserDto {
  @Transform(({ value }) => trim(value))
  @IsString()
  @IsNotEmpty()
  username: string;

  @Transform(({ value }) => trim(value))
  @IsStrongPassword(strongPasswordPolicy)
  @IsNotEmpty()
  password: string;

  @IsBoolean()
  @IsOptional()
  createNewDemoUser: boolean;
}
