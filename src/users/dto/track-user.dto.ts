import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsPositive, IsString, Max, <PERSON> } from 'class-validator';
import { Transform } from 'class-transformer';
import { trim } from '../../common/transform-helpers';

export class TrackUserDto {
  @IsNumber()
  @IsPositive()
  @IsNotEmpty()
  appVersionCode: number;

  @Transform(({ value }) => trim(value))
  @IsString()
  @IsNotEmpty()
  appVersionNumber: string;

  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  batteryLevel: number;

  @IsNumber()
  @Min(0)
  @Max(100)
  @IsNotEmpty()
  volumeLevel: number;

  @Transform(({ value }) => trim(value))
  @IsString()
  @IsNotEmpty()
  buildSerial: string;

  @Transform(({ value }) => trim(value))
  @IsString()
  @IsNotEmpty()
  deviceId: string;

  @Transform(({ value }) => trim(value))
  @IsString()
  @IsNotEmpty()
  deviceModel: string;

  @Transform(({ value }) => trim(value))
  @IsString()
  @IsNotEmpty()
  deviceOSVersion: string;

  @IsNumber()
  @IsOptional()
  latitude?: number;

  @IsNumber()
  @IsOptional()
  longitude?: number;

  @IsNumber()
  @IsOptional()
  accuracy?: number;
}
