import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { UsersService } from './users.service';
import {
  CreateUserDto,
  LoginUserDto,
  TrackUserDto,
  UpdateUserDto,
  UserIdDto,
  UserPasswordDto,
  UserPortDto,
  UserUsernameDto,
} from './dto';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';
import { FastifyRequest } from 'fastify';
import { AuthGuard } from '@nestjs/passport';

@UseGuards(AuthGuard('api-key'))
@Controller('users')
export class UsersController {
  /**
   * Inject the UsersService into the controller
   *
   * @param {UsersService} usersService
   */
  constructor(private readonly usersService: UsersService) {}

  /**
   * Endpoint to login a user
   *
   * @param {LoginUserDto} userCredentials
   * @returns {Promise<{portNumber: any}>}
   */
  @Post('/login')
  login(@Body() userCredentials: LoginUserDto) {
    return this.usersService.login(userCredentials);
  }

  /**
   * Endpoint to create a new user
   *
   * @param {CreateUserDto} createUserDto
   * @returns {Promise<HydratedDocument<UserDocument, {}, {}>>}
   */
  @Post()
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  /**
   * Endpoint to retrieve all users
   *
   * @returns {Promise<Array<HydratedDocument<UserDocument, {}, {}>>>}
   */
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(15000)
  @Get()
  findAll() {
    return this.usersService.findAll();
  }

  /**
   * Endpoint to retrieve all users that have a specific port number
   *
   * @param {UserPortDto} portNumber
   * @returns {Promise<{companyName: any}[]>}
   */
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(15000)
  @Get('/port/:portNumber')
  findAllByPortNumber(@Param() portNumber: UserPortDto) {
    return this.usersService.findAllByPortNumber(portNumber);
  }

  /**
   * Endpoint to retrieve a user by its id
   *
   * @param {UserIdDto} id
   * @returns {Promise<User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  @Get(':id')
  findOne(@Param() id: UserIdDto) {
    return this.usersService.findOneById(id);
  }

  /**
   * Endpoint to retrieve a user by its username
   *
   * @param {UserUsernameDto} username
   * @returns {Promise<User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  @Get('/username/:username')
  findOneByUsername(@Param() username: UserUsernameDto) {
    return this.usersService.findOneByUsername(username);
  }

  /**
   * Endpoint to update a user by its id
   *
   * @param {UserIdDto} id
   * @param {UpdateUserDto} updateUserDto
   * @returns {Query<User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId}) | null, User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId}), {}, User & Document<any, any, any>> & {}}
   */
  @Patch(':id')
  update(@Param() id: UserIdDto, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(id, updateUserDto);
  }

  /**
   * Endpoint to update a user's last tracking data
   *
   * @param {FastifyRequest} request
   * @param {UserUsernameDto} username
   * @param {UserPasswordDto} password
   * @param {TrackUserDto} userTrackingData
   * @returns {Promise<User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  @Patch('/lastMobileStatus/:username/:password')
  updateLastMobileStatus(
    @Req() request: FastifyRequest,
    @Param() username: UserUsernameDto,
    @Param() password: UserPasswordDto,
    @Body() userTrackingData: TrackUserDto,
  ) {
    return this.usersService.updateLastMobileStatus(request, username, password, userTrackingData);
  }

  /**
   * Endpoint to delete a user by its id
   *
   * @param {LoginUserDto} userCredentials
   * @returns {Query<User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId}) | null, User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId}), {}, User & Document<any, any, any>> & {}}
   */
  @Delete()
  remove(@Body() userCredentials: LoginUserDto) {
    return this.usersService.remove(userCredentials);
  }
}
