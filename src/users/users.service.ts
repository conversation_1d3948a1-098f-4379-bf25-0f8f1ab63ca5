import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateUserDto,
  LoginUserDto,
  TrackUserDto,
  UpdateUserDto,
  UserIdDto,
  UserPasswordDto,
  UserPortDto,
  UserUsernameDto,
} from './dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { User, UserDocument } from './schemas/user.schema';
import { Company, CompanyDocument } from '../companies/schemas/company.schema';
import { FastifyRequest } from 'fastify';
import { getConfiguration } from '../utils/configuration';

@Injectable()
export class UsersService {
  /**
   * Inject the User and Company models into the service
   *
   * @param {Model<UserDocument>} userModel
   * @param {Model<CompanyDocument>} companyModel
   */
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(Company.name) private companyModel: Model<CompanyDocument>,
  ) {}

  /**
   * Helper function to create a demo user
   *
   * @param {string} username
   * @param {string} password
   * @param {boolean} createNewDemoUser
   * @param {(Company & {_id: Types.ObjectId})[]} companies
   * @returns {Promise<void>}
   */
  async createDemoUser(
    { username, password, createNewDemoUser }: LoginUserDto,
    companies: (Company & { _id: Types.ObjectId })[],
  ) {
    if (createNewDemoUser && password === 'demo123') {
      const demoCompanyPort = Number(getConfiguration()?.mumble?.portRangeStart) || 64740;
      const demoCompany = companies.find(company => company.portNumber === demoCompanyPort);

      if (!demoCompany) {
        throw new NotFoundException(['No Demo Company found. Please try again later.']);
      }

      const demoUser = await this.userModel.findOneAndUpdate(
        { username },
        {
          $set: {
            firstName: 'Demo',
            lastName: 'User',
            username,
            password,
            phone: '+************',
            companyId: demoCompany._id,
          },
        },
        { upsert: true, new: true },
      );

      if (!demoUser) {
        throw new InternalServerErrorException([
          'Could not create Demo User. Please try again later.',
        ]);
      }
    }
  }

  /**
   * Login a user.
   * Normal login for commercial users.
   * Demo account creation and login for demo users.
   *
   * @param {LoginUserDto} loginUserDto
   * @returns {Promise<{portNumber: any}>}
   */
  async login(loginUserDto: LoginUserDto) {
    const { username, password } = loginUserDto;
    const companies = (await this.companyModel.find().exec()) as (Company & {
      _id: Types.ObjectId;
    })[];

    await this.createDemoUser(loginUserDto, companies);

    const user = await this.userModel.findOne({ username, password }).lean().exec();

    if (!user) {
      throw new NotFoundException([
        'No User account found with these credentials. Please try again.',
      ]);
    }

    const userCompany = companies.find(
      company => company._id?.toString() === user.companyId.toString(),
    );

    if (!userCompany) {
      throw new NotFoundException(['No Company found for this user. Please try again.']);
    }

    return { ...user, portNumber: userCompany.portNumber };
  }

  /**
   * Create a new user
   *
   * @param {CreateUserDto} createUserDto
   * @returns {Promise<HydratedDocument<UserDocument, {}, {}>>}
   */
  async create(createUserDto: CreateUserDto) {
    const existingUser = await this.userModel
      .findOne({
        username: createUserDto.username,
      })
      .exec();

    if (existingUser) {
      throw new BadRequestException([
        'A user with this username already exists. Please try again.',
      ]);
    }

    return this.userModel.create(createUserDto);
  }

  /**
   * Retrieve all users
   *
   * @returns {Promise<Array<HydratedDocument<UserDocument, {}, {}>>>}
   */
  async findAll() {
    const users = await this.userModel
      .find(
        {},
        '-updatedAt -accuracy -appVersionCode -buildSerial -deviceId -deviceModel -deviceOSVersion',
      )
      .exec();

    if (!users || users.length === 0) {
      throw new NotFoundException(['No users found.']);
    }

    return users;
  }

  /**
   * Retrieve all users using a port number (same company)
   *
   * @param {number} portNumber
   * @returns {Promise<{companyName: any}[]>}
   */
  async findAllByPortNumber({ portNumber }: UserPortDto) {
    const companyFromPortNumber = await this.companyModel
      .findOne({
        portNumber,
      })
      .exec();

    if (!companyFromPortNumber) {
      throw new NotFoundException(['No company found for this port number.']);
    }

    const users = await this.userModel
      .find(
        {
          companyId: companyFromPortNumber._id,
        },
        '-updatedAt -accuracy -appVersionCode -buildSerial -deviceId -deviceModel -deviceOSVersion',
      )
      .lean()
      .exec();

    if (!users || users.length === 0) {
      throw new NotFoundException(['No users found for this port number.']);
    }

    return users.map(user => ({
      ...user,
      companyName: companyFromPortNumber.companyName,
    }));
  }

  /**
   * Retrieve a user by its id
   *
   * @param {string} id
   * @returns {Promise<User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async findOneById({ id }: UserIdDto) {
    const user = await this.userModel.findById(id).exec();

    if (!user) {
      throw new NotFoundException(['No user found with this id.']);
    }

    return user;
  }

  /**
   * Retrieve a user by its username
   *
   * @param {string} username
   * @returns {Promise<User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async findOneByUsername({ username }: UserUsernameDto) {
    const user = await this.userModel.findOne({ username }).exec();

    if (!user) {
      throw new NotFoundException(['No user found with this username.']);
    }

    return user;
  }

  /**
   * Delete a user by its username and password
   *
   * @param {string} username
   * @param {string} password
   * @returns {Query<User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId}) | null, User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId}), {}, User & Document<any, any, any>> & {}}
   */
  remove({ username, password }: LoginUserDto) {
    const deletedUser = this.userModel.findOneAndDelete({
      username,
      password,
    });

    if (!deletedUser) {
      throw new NotFoundException(['No user found with these credentials.']);
    }

    return deletedUser;
  }

  /**
   * Update a user by its id
   *
   * @param {string} id
   * @param {UpdateUserDto} updateUserDto
   * @returns {Query<User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId}) | null, User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId}), {}, User & Document<any, any, any>> & {}}
   */
  update({ id }: UserIdDto, updateUserDto: UpdateUserDto) {
    const updatedUser = this.userModel.findByIdAndUpdate(id, updateUserDto);

    if (!updatedUser) {
      throw new NotFoundException(['No user found with this id.']);
    }

    return updatedUser;
  }

  /**
   * Update a user with its last tracking data
   *
   * @param {FastifyRequest} request
   * @param {string} username
   * @param {string} password
   * @param {TrackUserDto} userTrackingData
   * @returns {Promise<User & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, User & Document<any, any, any> & {_id: Types.ObjectId}, User & Document<any, any, any> & Required<{_id: U}>> : (User & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async updateLastMobileStatus(
    request: FastifyRequest,
    { username }: UserUsernameDto,
    { password }: UserPasswordDto,
    userTrackingData: TrackUserDto,
  ) {
    const { longitude, latitude } = userTrackingData;

    // Retrieve the user's IP address
    const forwardedIP = request?.ip;
    const deviceExternalIPv6 = forwardedIP ? forwardedIP : request?.socket?.remoteAddress;
    const deviceExternalIP =
      deviceExternalIPv6?.substr(0, 7) === '::ffff:'
        ? deviceExternalIPv6.substr(7)
        : deviceExternalIPv6;

    // Retrieve the user's country - only if location data is available
    const locationDataAvailable = !!longitude && !!latitude;
    let country = 'N/A';

    if (locationDataAvailable) {
      try {
        const { findCountryByCoordinate } = await import('country-locator');
        country = findCountryByCoordinate(latitude, longitude)?.code ?? 'N/A';
      } catch (error) {
        country = 'N/A';
      }
    }

    const preformattedTimestamp = Number(new Date().getTime().toString());
    const lastRecordTimestamp =
      preformattedTimestamp > 2147483647
        ? Math.trunc(preformattedTimestamp / 1000)
        : preformattedTimestamp;

    const updatedUserTrackingData = await this.userModel
      .findOneAndUpdate(
        { username, password },
        {
          $set: {
            ...userTrackingData,
            deviceExternalIP,
            country,
            lastRecordTimestamp,
          },
        },
        {
          upsert: false,
          new: true,
        },
      )
      .exec();

    if (!updatedUserTrackingData) {
      throw new NotFoundException(['No user found with these credentials.']);
    }

    return updatedUserTrackingData;
  }
}
