import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AdminsModule } from './admins/admins.module';
import { MongooseModule } from '@nestjs/mongoose';
import { CompaniesModule } from './companies/companies.module';
import { ManagersModule } from './managers/managers.module';
import { UsersModule } from './users/users.module';
import { ConfigModule } from '@nestjs/config';
import { getConfiguration } from './utils/configuration';
import { ServerStatsModule } from './server-stats/server-stats.module';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { APP_GUARD } from '@nestjs/core';
import { AuthModule } from './auth/auth.module';
import { ScheduleModule } from '@nestjs/schedule';
import { CronsModule } from './crons/crons.module';
import { CacheModule } from '@nestjs/cache-manager';

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: `.env.${getConfiguration().server.environment}`,
      isGlobal: true,
    }),
    CacheModule.register({
      isGlobal: true,
      ttl: 10000,
      max: 500,
    }),
    ...(getConfiguration().server.isDemo
      ? [
          ThrottlerModule.forRoot([
            {
              name: 'default',
              ttl: 60000,
              limit: 120,
            },
          ]),
          ScheduleModule.forRoot(),
          CronsModule,
        ]
      : []),
    AuthModule,
    MongooseModule.forRoot(getConfiguration().database.connectionUrl),
    AdminsModule,
    CompaniesModule,
    ManagersModule,
    UsersModule,
    ServerStatsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    ...(getConfiguration().server.isDemo ? [{ provide: APP_GUARD, useClass: ThrottlerGuard }] : []),
  ],
})
export class AppModule {}
