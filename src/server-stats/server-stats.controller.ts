import { Controller, Get, UseGuards, UseInterceptors } from '@nestjs/common';
import { ServerStatsService } from './server-stats.service';
import { AuthGuard } from '@nestjs/passport';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

@UseGuards(AuthGuard('api-key'))
@Controller('serverstats')
export class ServerStatsController {
  constructor(private readonly serverStatsService: ServerStatsService) {}

  @UseInterceptors(CacheInterceptor)
  @CacheTTL(30000)
  @Get()
  findAll() {
    return this.serverStatsService.findAll();
  }
}
