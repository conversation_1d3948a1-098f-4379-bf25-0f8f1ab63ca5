import { Injectable } from '@nestjs/common';
import si from 'systeminformation';
import relativeTime from 'dayjs/plugin/relativeTime';
import dayjs from 'dayjs';

dayjs.extend(relativeTime);

// Helper function to capitalize the first letter of a string
const capitalizeFirstLetter = (str: string) => str.charAt(0).toUpperCase() + str.slice(1);

// Helper function to convert bytes to gigabytes
const bytesToGb = (bytes: number) => bytes / Math.pow(2, 30);

// Helper function to retrieve the server's stats
const getServerStats = async () => {
  const [
    cpuInfo,
    currentLoadInfo,
    processesInfo,
    osInfo,
    uptimeInfo,
    memoryInfo,
    hardDriveInfo,
    interfaceInfo,
    pingInfo,
    externalIP,
  ] = await Promise.all([
    si.cpu(),
    si.currentLoad(),
    si.processes(),
    si.osInfo(),
    si.time().uptime,
    si.mem(),
    si.fsSize(),
    si.networkInterfaces(),
    si.inetChecksite('https://google.com'),
    //@ts-ignore
    import('node-public-ip').then(async ({ publicIp }) => await publicIp()),
  ]);

  const interfaceSpeed =
    //@ts-ignore
    interfaceInfo.filter(
      //@ts-ignore
      iface => !iface.internal && iface.operstate === 'up' && iface?.speed && iface?.speed > 0,
    )[0]?.speed ?? 100;

  // I want to get the object that has the biggest number in the size property of the hardDriveInfo array
  const primaryFileSystem = hardDriveInfo?.reduce((prev, current) =>
    prev.size > current.size ? prev : current,
  );

  const hardDriveStats = {
    totalGb: bytesToGb(primaryFileSystem.size).toFixed(2),
    usedGb: bytesToGb(primaryFileSystem.used).toFixed(2),
  };

  const networkStats = {
    externalIP,
    interfaceSpeed: interfaceSpeed > 0 ? interfaceSpeed : null,
    latency: pingInfo.ms > 0 ? Math.floor(pingInfo.ms / 3) : null,
  };

  const totalMemMb = memoryInfo.total * Math.pow(2, -20);
  const usedMemMb = memoryInfo.active * Math.pow(2, -20);
  const freeMemMb = totalMemMb - usedMemMb;
  const freeMemPercentage = (freeMemMb / totalMemMb) * 100;

  const cpuStats = {
    cpuModel: `${cpuInfo.manufacturer} ${cpuInfo.brand} @${cpuInfo.speedMax}GHz`,
    cpuCores: cpuInfo.physicalCores ?? cpuInfo.cores,
    cpuUsedPercentage: currentLoadInfo.currentLoad.toFixed(2),
    cpuFreePercentage: currentLoadInfo.currentLoadIdle.toFixed(2),
  };

  const osStats = {
    os: `${osInfo.distro} ${osInfo.release} ${osInfo.arch}`,
    numberOfProcesses: processesInfo.all,
    uptimeSeconds: capitalizeFirstLetter(dayjs().add(uptimeInfo, 'second').fromNow(true)),
  };

  const ramStats = {
    totalMemMb: totalMemMb.toFixed(2),
    usedMemMb: usedMemMb.toFixed(2),
    freeMemMb: freeMemMb.toFixed(2),
    freeMemPercentage: freeMemPercentage.toFixed(2),
  };

  return {
    osStats,
    cpuStats,
    ramStats,
    hardDriveStats,
    networkStats,
  };
};

@Injectable()
export class ServerStatsService {
  findAll() {
    return getServerStats();
  }
}
