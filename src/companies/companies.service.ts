import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import { CompanyIdDto, CreateCompanyDto, UpdateCompanyDto } from './dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Company, CompanyDocument, CompanyLean } from './schemas/company.schema';
import { User, UserDocument } from '../users/schemas/user.schema';
import { Manager, ManagerDocument } from '../managers/schemas/manager.schema';
import { getConfiguration } from '../utils/configuration';
import { exec, execSync } from 'child_process';

@Injectable()
export class CompaniesService {
  /**
   * Inject the Company,User and Manager models into the service
   * @param {Model<CompanyDocument>} companyModel
   * @param {Model<UserDocument>} userModel
   * @param {Model<ManagerDocument>} managerModel
   */
  constructor(
    @InjectModel(Company.name) private companyModel: Model<CompanyDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(Manager.name) private managerModel: Model<ManagerDocument>,
  ) {}

  /**
   * Helper function to retrieve a port number to be used in the company creation
   *
   * @param {Company[]} companies
   * @returns {number}
   */
  retrieveNewPortNumber(companies: Company[]) {
    const portNumbersTaken = companies
      .map(company => {
        return company.portNumber;
      })
      .sort();

    const portRangeStartString = getConfiguration()?.mumble?.portRangeStart?.toString() || '64740';
    let portToUse = Number(portRangeStartString);

    // Find which port starting from the mumblePortRangeStart is available
    for (let i = 0; i < portNumbersTaken.length; i++) {
      if (portToUse !== portNumbersTaken[i]) {
        break;
      }
      portToUse++;
    }

    return portToUse;
  }

  /**
   * Helper function to spin up a docker container
   *
   * @param {number} portNumber
   * @param {CreateCompanyDto} companyIn
   * @returns {Promise<{status: number, message: string, dockerId?: string}>}
   */
  spinUpContainer(
    portNumber: number,
    companyIn: CreateCompanyDto,
  ): Promise<{ status: number; message: string; dockerId?: string }> {
    return new Promise(async (resolve, _) => {
      try {
        // Transform the company name to lowercase with underscores to successfully start a container
        const formattedCompanyIn = companyIn.companyName.toLowerCase().trim().split(' ').join('_');

        execSync(
          `docker run -d -p ${portNumber}:64738 -p ${portNumber}:64738/udp -v mumble-data-${portNumber}-${formattedCompanyIn}:/etc/mumble --restart=always --name ${formattedCompanyIn}-server phlak/mumble`,
        );
        const dockerId = execSync(`docker ps -qf "name=^${formattedCompanyIn}-server$"`)
          .toString()
          .trim();
        execSync(
          `docker exec ${formattedCompanyIn}-server sed -i "$ a autobanAttempts=20\\nautobanTimeframe=120\\nautobanTime=300\\nserverpassword=${
            getConfiguration()?.mumble?.serverPass
          }\\nbandwidth=16000\\nusers=50\\nregisterName=${
            getConfiguration()?.mumble?.serverName
          }" /etc/mumble/config.ini`,
        );
        exec(`docker restart ${formattedCompanyIn}-server`);

        resolve({
          status: 200,
          message: 'Success',
          dockerId,
        });
      } catch (error) {
        resolve({
          status: 500,
          message: `Error creating Docker Container. Reason: ${error}`,
        });
      }
    });
  }

  /**
   * Helper function to edit an already running docker container
   *
   * @param {string} newCompanyNameIn
   * @param {string} dockerId
   * @returns {Promise<{status: number, message: string}>}
   */
  editRunningContainer(
    newCompanyNameIn: string,
    dockerId: string,
  ): Promise<{ status: number; message: string }> {
    return new Promise(async (resolve, _) => {
      try {
        // Transform the company name to lowercase with underscores to successfully start a container
        const formattedCompanyNameIn = newCompanyNameIn.toLowerCase().trim().split(' ').join('_');
        execSync(`docker rename ${dockerId} ${formattedCompanyNameIn}-server`);
        exec(`docker restart ${dockerId}`);
        resolve({
          status: 200,
          message: 'Success',
        });
      } catch (error) {
        resolve({
          status: 500,
          message: `Error editing Docker Container. Reason: ${error}`,
        });
      }
    });
  }

  /**
   * Helper function to count the users and the managers in each company and return them with it
   * Uses aggregation pipelines to count efficiently at database level and returns the results
   * in a map for O(1) access
   * @param {FlattenMaps<CompanyDocument>[]} companies
   * @returns {Promise<{users: number, managers: number}[]>}
   */
  getCountUsersManagers = async (companies: CompanyLean[]) => {
    const companiesLength = companies.length;

    if (companiesLength === 0) {
      return [];
    }

    const companyIds = companies.map(company => company._id);

    const [usersCountResult, managersCountResult] = await Promise.all([
      this.userModel.aggregate([
        { $match: { companyId: { $in: companyIds } } },
        { $group: { _id: '$companyId', count: { $sum: 1 } } },
      ]),
      this.managerModel.aggregate([
        { $match: { companyId: { $in: companyIds } } },
        { $group: { _id: '$companyId', count: { $sum: 1 } } },
      ]),
    ]);

    const usersCountMap = new Map(usersCountResult.map(item => [item._id.toString(), item.count]));
    const managersCountMap = new Map(
      managersCountResult.map(item => [item._id.toString(), item.count]),
    );

    return companies.map(company => ({
      ...company,
      users: usersCountMap.get(company._id.toString()) || 0,
      managers: managersCountMap.get(company._id.toString()) || 0,
    }));
  };

  /**
   * Create a new company
   *
   * @param {CreateCompanyDto} createCompanyDto
   * @returns {Promise<HydratedDocument<CompanyDocument, {}, {}>>}
   */
  async create(createCompanyDto: CreateCompanyDto) {
    const { companyName, email, country, address, phone } = createCompanyDto;

    const companies = await this.companyModel.find().exec();

    for (const company of companies) {
      if (company.email === email) {
        throw new NotAcceptableException(['This Company already exists. Please try again.']);
      }
    }

    const portNumber = this.retrieveNewPortNumber(companies);

    const dockerContainer = await this.spinUpContainer(portNumber, createCompanyDto);

    if (dockerContainer.status !== 200 || !dockerContainer.dockerId) {
      throw new BadRequestException([
        'Unable to spin up a docker container. Reason ' + dockerContainer.message,
      ]);
    }

    return this.companyModel.create({
      companyName,
      email,
      country,
      address,
      phone,
      portNumber,
      dockerId: dockerContainer.dockerId,
    });
  }

  /**
   * Get all companies with the number of users and managers
   *
   * @returns {Promise<Array<HydratedDocument<CompanyDocument, {}, {}>>>}
   */
  async findAll() {
    const companies = await this.companyModel.find({}, '-updatedAt').lean().exec();

    if (!companies || companies.length === 0) {
      throw new NotFoundException(['No companies found']);
    }

    const companiesWithStringId = companies.map(company => ({
      ...company,
      _id: company._id.toString(),
    }));

    return await this.getCountUsersManagers(companiesWithStringId);
  }

  /**
   * Find a company by id
   *
   * @param {string} id
   * @returns {Promise<Company & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Company & Document<any, any, any> & {_id: Types.ObjectId}, Company & Document<any, any, any> & Required<{_id: U}>> : (Company & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async findOne({ id }: CompanyIdDto) {
    const company = await this.companyModel.findById(id).exec();

    if (!company) {
      throw new NotFoundException(['Admin not found']);
    }

    return company;
  }

  /**
   * Update a company by id
   *
   * @param {string} id
   * @param {UpdateCompanyDto} updateCompanyDto
   * @returns {Promise<Query<Company & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Company & Document<any, any, any> & {_id: Types.ObjectId}, Company & Document<any, any, any> & Required<{_id: U}>> : (Company & Document<any, any, any> & {_id: Types.ObjectId}) | null, Company & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Company & Document<any, any, any> & {_id: Types.ObjectId}, Company & Document<any, any, any> & Required<{_id: U}>> : (Company & Document<any, any, any> & {_id: Types.ObjectId}), {}, Company & Document<any, any, any>> & {}>}
   */
  async update({ id }: CompanyIdDto, updateCompanyDto: UpdateCompanyDto) {
    const { companyName, email, country, address, phone, portNumber, dockerId } = updateCompanyDto;

    const editRunningContainerResult = await this.editRunningContainer(companyName, dockerId);

    if (
      editRunningContainerResult?.status === 500 &&
      !editRunningContainerResult?.message?.includes('same name as')
    ) {
      throw new InternalServerErrorException([editRunningContainerResult?.message]);
    }

    const updatedCompany = await this.companyModel.findOneAndUpdate(
      { _id: id },
      {
        $set: {
          companyName,
          email,
          address,
          phone,
          portNumber,
          dockerId,
          country,
        },
      },
      { upsert: false, new: true },
    );

    if (!updatedCompany) {
      throw new NotFoundException([
        'No Companies with this information to edit were found. Please try again.',
      ]);
    }

    return updatedCompany;
  }

  /**
   * Remove a company by id
   *
   * @param {string} companyId
   * @returns {Promise<Awaited<DeleteResult>[]>}
   */
  async remove({ id: companyId }: CompanyIdDto) {
    // Check if company exists
    const companyToDelete = await this.companyModel.findById(companyId).exec();

    if (!companyToDelete) {
      throw new NotFoundException(['No company found to delete. Please try again.']);
    }

    const { companyName, portNumber, dockerId } = companyToDelete;

    // Delete any running containers with the corresponding docker ID
    try {
      execSync(`docker rm --force ${dockerId}`);
    } catch (error) {
      throw new InternalServerErrorException([
        `No container found for ${companyName}. Please try again.`,
      ]);
    }

    // Delete the company record as well as all of its depending managers and users
    return Promise.all([
      this.companyModel.deleteOne({ _id: companyId }).exec(),
      this.managerModel.deleteMany({ companyId }).exec(),
      this.userModel.deleteMany({ companyId }).exec(),
    ]);
  }
}
