import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type CompanyDocument = Company & Document;

export interface CompanyLean {
  _id: string;
  companyName: string;
  address: string;
  email: string;
  phone: string;
  country: string;
  portNumber: number;
  dockerId: string;
}

@Schema({ timestamps: true, autoCreate: true })
export class Company {
  @Prop({ required: true })
  companyName: string;

  @Prop({ required: true })
  address: string;

  @Prop({ required: true, index: true, unique: true })
  email: string;

  @Prop({ required: true })
  phone: string;

  @Prop({ required: true })
  country: string;

  @Prop({ required: true, index: true, unique: true })
  portNumber: number;

  @Prop({ required: true })
  dockerId: string;
}

export const CompanySchema = SchemaFactory.createForClass(Company);
