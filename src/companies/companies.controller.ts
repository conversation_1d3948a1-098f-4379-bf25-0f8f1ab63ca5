import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CompaniesService } from './companies.service';
import { CompanyIdDto, CreateCompanyDto, UpdateCompanyDto } from './dto';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';
import { AuthGuard } from '@nestjs/passport';

@UseGuards(AuthGuard('api-key'))
@Controller('companies')
export class CompaniesController {
  /**
   * Inject the CompaniesService into the controller
   *
   * @param {CompaniesService} companiesService
   */
  constructor(private readonly companiesService: CompaniesService) {}

  /**
   * Endpoint to create a new company
   *
   * @param {CreateCompanyDto} createCompanyDto
   * @returns {Promise<HydratedDocument<CompanyDocument, {}, {}>>}
   */
  @Post()
  create(@Body() createCompanyDto: CreateCompanyDto) {
    return this.companiesService.create(createCompanyDto);
  }

  /**
   * Endpoint to retrieve all companies
   *
   * @returns {Promise<Array<HydratedDocument<CompanyDocument, {}, {}>>>}
   */
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(30000)
  @Get()
  findAll() {
    return this.companiesService.findAll();
  }

  /**
   * Endpoint to retrieve a company by its id
   *
   * @param {CompanyIdDto} id
   * @returns {Promise<Company & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Company & Document<any, any, any> & {_id: Types.ObjectId}, Company & Document<any, any, any> & Required<{_id: U}>> : (Company & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  @Get(':id')
  findOne(@Param() id: CompanyIdDto) {
    return this.companiesService.findOne(id);
  }

  /**
   * Endpoint to update a company by its id
   *
   * @param {CompanyIdDto} id
   * @param {UpdateCompanyDto} updateCompanyDto
   * @returns {Promise<Query<Company & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Company & Document<any, any, any> & {_id: Types.ObjectId}, Company & Document<any, any, any> & Required<{_id: U}>> : (Company & Document<any, any, any> & {_id: Types.ObjectId}) | null, Company & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Company & Document<any, any, any> & {_id: Types.ObjectId}, Company & Document<any, any, any> & Required<{_id: U}>> : (Company & Document<any, any, any> & {_id: Types.ObjectId}), {}, Company & Document<any, any, any>> & {}>}
   */
  @Patch(':id')
  update(@Param() id: CompanyIdDto, @Body() updateCompanyDto: UpdateCompanyDto) {
    return this.companiesService.update(id, updateCompanyDto);
  }

  /**
   * Endpoint to delete a company by its id
   *
   * @param {CompanyIdDto} id
   * @returns {Promise<Awaited<DeleteResult>[]>}
   */
  @Delete(':id')
  remove(@Param() id: CompanyIdDto) {
    return this.companiesService.remove(id);
  }
}
