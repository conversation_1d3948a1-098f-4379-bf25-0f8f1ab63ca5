import { IsEmail, IsNotEmpty, IsPhone<PERSON>umber, IsString, Max<PERSON>ength } from 'class-validator';
import { Transform } from 'class-transformer';
import { toLowerCase, toUpperCase, trim } from '../../common/transform-helpers';

export class CreateCompanyDto {
  @Transform(({ value }) => trim(value))
  @IsString()
  @IsNotEmpty()
  companyName: string;

  @Transform(({ value }) => trim(value))
  @IsString()
  @IsNotEmpty()
  address: string;

  @Transform(({ value }) => toLowerCase(value))
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @Transform(({ value }) => trim(value))
  @IsPhoneNumber()
  @IsNotEmpty()
  phone: string;

  @Transform(({ value }) => trim(value))
  @Transform(({ value }) => toUpperCase(value))
  @IsString()
  @MaxLength(3)
  @IsNotEmpty()
  country: string;
}
