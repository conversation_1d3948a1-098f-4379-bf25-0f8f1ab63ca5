import { CreateCompanyDto } from './create-company.dto';
import { IsNotEmpty, IsNumber, IsPositive, IsString, Max } from 'class-validator';
import { Transform } from 'class-transformer';
import { trim } from '../../common/transform-helpers';

export class UpdateCompanyDto extends CreateCompanyDto {
  @Transform(({ value }) => trim(value))
  @IsString()
  @IsNotEmpty()
  dockerId: string;

  @IsNumber()
  @IsPositive()
  @Max(65534)
  portNumber: number;
}
