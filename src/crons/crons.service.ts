import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Model } from 'mongoose';
import { User, UserDocument } from 'src/users/schemas/user.schema';

@Injectable()
export class CronsService {
  constructor(@InjectModel(User.name) private userModel: Model<UserDocument>) {}

  @Cron(CronExpression.EVERY_6_HOURS)
  handleCron() {
    // Delete all demo users with empty tracking data
    this.userModel
      .deleteMany({ $or: [{ latitude: null }, { latitude: 0 }, { latitude: '0' }] })
      .exec();
    // Delete all users that were created more than 90 days ago
    const deletionDate = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString();
    this.userModel.deleteMany({ createdAt: { $lt: deletionDate } }).exec();
  }
}
