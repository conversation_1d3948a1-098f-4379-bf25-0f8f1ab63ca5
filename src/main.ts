import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { Logger, ValidationPipe } from '@nestjs/common';
import { getConfiguration } from './utils/configuration';
import { readFileSync } from 'fs';
import compression from '@fastify/compress';
import helmet from '@fastify/helmet';
import { constants } from 'zlib';

async function bootstrap() {
  const bootstrapStartTimestamp = performance.now();
  const serverConfiguration = getConfiguration().server;
  const useHttps = serverConfiguration.isProduction && serverConfiguration.https;

  const fastifyOptions = {
    logger: false,
    maxParamLength: 100,
    bodyLimit: 5242880, // 5MB
    connectionTimeout: 30000,
    keepAliveTimeout: 5000,
    ...(useHttps && {
      https: {
        key: readFileSync(serverConfiguration.certificatesPath + '/privkey.pem', 'utf8'),
        cert: readFileSync(serverConfiguration.certificatesPath + '/fullchain.pem', 'utf8'),
      },
    }),
  };

  const fastifyAdapter = new FastifyAdapter(fastifyOptions);

  const app = await NestFactory.create<NestFastifyApplication>(AppModule, fastifyAdapter);

  await Promise.all([
    app.register(compression, {
      brotliOptions: {
        [constants.BROTLI_PARAM_QUALITY]: 4,
      },
      zlibOptions: {
        level: 6,
      },
      threshold: 1024, // Only compress responses > 1KB
    }),
    app.register(helmet),
  ]);

  app.enableCors();
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true, // Reject unknown properties
      skipMissingProperties: false,
    }),
  );

  await app.listen(serverConfiguration.port, '0.0.0.0');

  const bootstrapDuration =
    Math.floor(((performance.now() - bootstrapStartTimestamp) * 100) / 1000) / 100;

  Logger.debug(
    `Listening at ${serverConfiguration.serverUrl}:${serverConfiguration.port} in ${bootstrapDuration}s`,
  );
}

bootstrap();
