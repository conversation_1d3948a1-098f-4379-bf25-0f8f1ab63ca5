import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, now } from 'mongoose';

export type AdminDocument = Admin & Document;

@Schema({ timestamps: true, autoCreate: true })
export class Admin {
  @Prop({ required: true })
  username: string;

  @Prop({ required: true })
  password: string;

  @Prop({ required: true })
  email: string;

  @Prop({ default: now() })
  createdAt: Date;
}

export const AdminSchema = SchemaFactory.createForClass(Admin);
