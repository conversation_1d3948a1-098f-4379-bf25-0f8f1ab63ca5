import { Injectable, NotFoundException } from '@nestjs/common';
import { AdminEmailDto, AdminIdDto, CreateAdminDto, LoginAdminDto } from './dto';
import { Admin, AdminDocument } from './schemas/admin.schema';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class AdminsService {
  /**
   * Inject the Admin model into the service
   *
   * @param {Model<AdminDocument>} adminModel
   */
  constructor(@InjectModel(Admin.name) private adminModel: Model<AdminDocument>) {}

  /**
   * Login an admin
   *
   * @param {string} email
   * @param {string} password
   * @returns {Promise<Admin & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Admin & Document<any, any, any> & {_id: Types.ObjectId}, Admin & Document<any, any, any> & Required<{_id: U}>> : (Admin & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async login({ email, password }: LoginAdminDto) {
    const admin = await this.adminModel.findOne({ email, password }).exec();

    if (!admin) {
      throw new NotFoundException([
        'No Administrator account found with these credentials. Please try again.',
      ]);
    }

    return admin;
  }

  /**
   * Create a new admin
   *
   * @param {CreateAdminDto} createAdminDto
   * @returns {QueryWithHelpers<HydratedDocument<AdminDocument, {}, {}>, HydratedDocument<AdminDocument, {}, {}>, {}, AdminDocument>}
   */
  create(createAdminDto: CreateAdminDto) {
    const { adminKey, ...adminParams } = createAdminDto;

    return this.adminModel.findOneAndUpdate(
      { email: adminParams.email },
      { $set: adminParams },
      { upsert: true, new: true },
    );
  }

  /**
   * Retrieve all admins
   *
   * @returns {Promise<Array<HydratedDocument<AdminDocument, {}, {}>>>}
   */
  async findAll() {
    const admins = await this.adminModel.find().exec();

    if (!admins || admins.length === 0) {
      throw new NotFoundException(['No admins found']);
    }

    return admins;
  }

  /**
   * Retrieve an admin by its id
   *
   * @param {string} id
   * @returns {Promise<Admin & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Admin & Document<any, any, any> & {_id: Types.ObjectId}, Admin & Document<any, any, any> & Required<{_id: U}>> : (Admin & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async findOneById({ id }: AdminIdDto) {
    const admin = await this.adminModel.findById(id).exec();

    if (!admin) {
      throw new NotFoundException(['Admin not found']);
    }

    return admin;
  }

  /**
   * Delete an admin by its id
   *
   * @param {string} id
   * @returns {Promise<Admin & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Admin & Document<any, any, any> & {_id: Types.ObjectId}, Admin & Document<any, any, any> & Required<{_id: U}>> : (Admin & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async removeById({ id }: AdminIdDto) {
    const deletedAdmin = await this.adminModel.findByIdAndDelete(id).exec();

    if (!deletedAdmin) {
      throw new NotFoundException(['Admin not found']);
    }

    return deletedAdmin;
  }

  /**
   * Find an admin by its email
   *
   * @param {string} email
   * @returns {Promise<Admin & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Admin & Document<any, any, any> & {_id: Types.ObjectId}, Admin & Document<any, any, any> & Required<{_id: U}>> : (Admin & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async findOneByEmail({ email }: AdminEmailDto) {
    const admin = await this.adminModel.findOne({ email }).exec();

    if (!admin) {
      throw new NotFoundException(['Admin not found']);
    }

    return admin;
  }

  /**
   * Delete an admin by its email
   *
   * @param {string} email
   * @returns {Promise<Admin & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Admin & Document<any, any, any> & {_id: Types.ObjectId}, Admin & Document<any, any, any> & Required<{_id: U}>> : (Admin & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async removeByEmail({ email }: AdminEmailDto) {
    const deletedAdmin = await this.adminModel.findOneAndDelete({ email }).exec();

    if (!deletedAdmin) {
      throw new NotFoundException(['Admin not found']);
    }

    return deletedAdmin;
  }
}
