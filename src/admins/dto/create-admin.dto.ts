import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsS<PERSON>, IsStrongPassword, Validate } from 'class-validator';
import { IsAdminKey, strongPasswordPolicy } from '../../common/validation-helpers';
import { Transform } from 'class-transformer';
import { toLowerCase, trim } from '../../common/transform-helpers';

export class CreateAdminDto {
  @Transform(({ value }) => trim(value))
  @IsString()
  @IsNotEmpty()
  username: string;

  @Transform(({ value }) => trim(value))
  @IsStrongPassword(strongPasswordPolicy)
  @IsNotEmpty()
  password: string;

  @Transform(({ value }) => toLowerCase(value))
  @IsEmail()
  @IsNotEmpty()
  email: string;

  // Check if user used the correct admin key
  @Transform(({ value }) => trim(value))
  @Validate(IsAdminKey, { message: 'Incorrect admin key entered' })
  adminKey: string;
}
