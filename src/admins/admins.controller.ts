import { Body, Controller, Delete, Get, Param, Post, UseGuards } from '@nestjs/common';
import { AdminsService } from './admins.service';
import { AdminEmailDto, AdminIdDto, CreateAdminDto, LoginAdminDto } from './dto';
import { AuthGuard } from '@nestjs/passport';

@UseGuards(AuthGuard('api-key'))
@Controller('admins')
export class AdminsController {
  /**
   * Inject the AdminsService into the controller
   *
   * @param {AdminsService} adminsService
   */
  constructor(private readonly adminsService: AdminsService) {}

  /**
   * Endpoint to login an admin
   *
   * @param {Pick<CreateAdminDto, "email" | "password">} adminCredentials
   * @returns {Promise<Array<HydratedDocument<AdminDocument, {}, {}>>>}
   */
  @Post('/login')
  login(@Body() adminCredentials: LoginAdminDto) {
    return this.adminsService.login(adminCredentials);
  }

  /**
   * Endpoint to create a new admin
   *
   * @param {CreateAdminDto} createAdminDto
   * @returns {Query<Admin & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Admin & Document<any, any, any> & {_id: Types.ObjectId}, Admin & Document<any, any, any> & Required<{_id: U}>> : (Admin & Document<any, any, any> & {_id: Types.ObjectId}), Admin & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Admin & Document<any, any, any> & {_id: Types.ObjectId}, Admin & Document<any, any, any> & Required<{_id: U}>> : (Admin & Document<any, any, any> & {_id: Types.ObjectId}), {}, Admin & Document<any, any, any>> & {}}
   */
  @Post()
  create(@Body() createAdminDto: CreateAdminDto) {
    return this.adminsService.create(createAdminDto);
  }

  /**
   * Endpoint to retrieve all admins
   *
   * @returns {Promise<Array<HydratedDocument<AdminDocument, {}, {}>>>}
   */
  @Get()
  findAll() {
    return this.adminsService.findAll();
  }

  /**
   * Endpoint to retrieve an admin by its id
   *
   * @param {AdminIdDto} id
   * @returns {Promise<Admin & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Admin & Document<any, any, any> & {_id: Types.ObjectId}, Admin & Document<any, any, any> & Required<{_id: U}>> : (Admin & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  @Get('/id/:id')
  findOneById(@Param() id: AdminIdDto) {
    return this.adminsService.findOneById(id);
  }

  /**
   * Endpoint to delete an admin by its id
   *
   * @param {AdminIdDto} id
   * @returns {Promise<Admin & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Admin & Document<any, any, any> & {_id: Types.ObjectId}, Admin & Document<any, any, any> & Required<{_id: U}>> : (Admin & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  @Delete('/id/:id')
  removeById(@Param() id: AdminIdDto) {
    return this.adminsService.removeById(id);
  }

  /**
   * Endpoint to find an admin by its email
   *
   * @param {AdminEmailDto} email
   * @returns {Promise<Admin & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Admin & Document<any, any, any> & {_id: Types.ObjectId}, Admin & Document<any, any, any> & Required<{_id: U}>> : (Admin & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  @Get('/email/:email')
  findOneByEmail(@Param() email: AdminEmailDto) {
    return this.adminsService.findOneByEmail(email);
  }

  /**
   * Endpoint to delete an admin by its email
   *
   * @param {AdminEmailDto} email
   * @returns {Promise<Admin & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Admin & Document<any, any, any> & {_id: Types.ObjectId}, Admin & Document<any, any, any> & Required<{_id: U}>> : (Admin & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  @Delete('/email/:email')
  removeByEmail(@Param() email: AdminEmailDto) {
    return this.adminsService.removeByEmail(email);
  }
}
