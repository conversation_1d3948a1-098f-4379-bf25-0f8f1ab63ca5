import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, now } from 'mongoose';
import { Company } from '../../companies/schemas/company.schema';

export type ManagerDocument = Manager & Document;

@Schema({ timestamps: true, autoCreate: true })
export class Manager {
  @Prop({ required: true })
  username: string;

  @Prop({ required: true })
  password: string;

  @Prop({ required: true, index: true, unique: true })
  email: string;

  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop({ default: now() })
  createdAt: Date;

  @Prop({ required: true })
  phone: string;

  @Prop({ required: true })
  country: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: Company.name, index: true })
  companyId: string;
}

export const ManagerSchema = SchemaFactory.createForClass(Manager);

// Add compound indexes for better query performance
ManagerSchema.index({ email: 1, password: 1 });
ManagerSchema.index({ companyId: 1 });
