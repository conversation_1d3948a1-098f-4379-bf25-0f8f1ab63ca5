import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ManagersService } from './managers.service';
import { CreateManagerDto, LoginManagerDto, ManagerIdDto, UpdateManagerDto } from './dto';
import { AuthGuard } from '@nestjs/passport';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

@UseGuards(AuthGuard('api-key'))
@Controller('managers')
export class ManagersController {
  /**
   * Inject the ManagersService into the controller
   *
   * @param {ManagersService} managersService
   */
  constructor(private readonly managersService: ManagersService) {}

  /**
   * Endpoint to login a manager
   *
   * @param {LoginManagerDto} managerCredentials
   * @returns {Promise<Manager & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Manager & Document<any, any, any> & {_id: Types.ObjectId}, Manager & Document<any, any, any> & Required<{_id: U}>> : (Manager & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  @Post('/login')
  login(@Body() managerCredentials: LoginManagerDto) {
    return this.managersService.login(managerCredentials);
  }

  /**
   * Endpoint to create a new manager
   *
   * @param {CreateManagerDto} createManagerDto
   * @returns {Query<Manager & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Manager & Document<any, any, any> & {_id: Types.ObjectId}, Manager & Document<any, any, any> & Required<{_id: U}>> : (Manager & Document<any, any, any> & {_id: Types.ObjectId}), Manager & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Manager & Document<any, any, any> & {_id: Types.ObjectId}, Manager & Document<any, any, any> & Required<{_id: U}>> : (Manager & Document<any, any, any> & {_id: Types.ObjectId}), {}, Manager & Document<any, any, any>> & {}}
   */
  @Post()
  create(@Body() createManagerDto: CreateManagerDto) {
    return this.managersService.create(createManagerDto);
  }

  /**
   * Endpoint to retrieve all managers
   *
   * @returns {Promise<Array<HydratedDocument<ManagerDocument, {}, {}>>>}
   */
  @UseInterceptors(CacheInterceptor)
  @CacheTTL(30000)
  @Get()
  findAll() {
    return this.managersService.findAll();
  }

  /**
   * Endpoint to retrieve a manager by their id
   *
   * @param {ManagerIdDto} id
   * @returns {string}
   */
  @Get(':id')
  findOne(@Param() id: ManagerIdDto) {
    return this.managersService.findOne(id);
  }

  /**
   * Endpoint to update a manager by its id
   *
   * @param {ManagerIdDto} id
   * @param {UpdateManagerDto} updateManagerDto
   * @returns {string}
   */
  @Patch(':id')
  update(@Param() id: ManagerIdDto, @Body() updateManagerDto: UpdateManagerDto) {
    return this.managersService.update(id, updateManagerDto);
  }

  /**
   * Endpoint to delete a manager by its id
   *
   * @param {ManagerIdDto} id
   * @returns {string}
   */
  @Delete(':id')
  remove(@Param() id: ManagerIdDto) {
    return this.managersService.remove(id);
  }
}
