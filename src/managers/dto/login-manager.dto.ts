import { Is<PERSON>mail, <PERSON>NotEmpty, IsStrongPassword } from 'class-validator';
import { strongPasswordPolicy } from '../../common/validation-helpers';
import { Transform } from 'class-transformer';
import { toLowerCase, trim } from '../../common/transform-helpers';

export class LoginManagerDto {
  @Transform(({ value }) => toLowerCase(value))
  @Transform(({ value }) => trim(value))
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @Transform(({ value }) => trim(value))
  @IsStrongPassword(strongPasswordPolicy)
  @IsNotEmpty()
  password: string;
}
