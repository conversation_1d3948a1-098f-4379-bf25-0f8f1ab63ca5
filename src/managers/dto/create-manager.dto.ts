import { Transform } from 'class-transformer';
import { toLowerCase, toUpperCase, trim } from '../../common/transform-helpers';
import {
  IsEmail,
  IsMongoId,
  IsNotEmpty,
  IsPhoneNumber,
  IsString,
  IsStrongPassword,
  MaxLength,
} from 'class-validator';
import { strongPasswordPolicy } from '../../common/validation-helpers';

export class CreateManagerDto {
  @Transform(({ value }) => trim(value))
  @IsString()
  @IsNotEmpty()
  username: string;

  @Transform(({ value }) => trim(value))
  @IsStrongPassword(strongPasswordPolicy)
  @IsNotEmpty()
  password: string;

  @Transform(({ value }) => toLowerCase(value))
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @Transform(({ value }) => trim(value))
  @IsPhoneNumber()
  @IsNotEmpty()
  phone: string;

  @Transform(({ value }) => trim(value))
  @Transform(({ value }) => toUpperCase(value))
  @IsString()
  @MaxLength(3)
  @IsNotEmpty()
  country: string;

  @IsString()
  @IsMongoId()
  @IsNotEmpty()
  companyId: string;
}
