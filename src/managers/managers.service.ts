import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateManagerDto, LoginManagerDto, ManagerIdDto, UpdateManagerDto } from './dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Manager, ManagerDocument } from './schemas/manager.schema';

@Injectable()
export class ManagersService {
  /**
   * Inject the Manager model into the service
   *
   * @param {Model<ManagerDocument>} managerModel
   */
  constructor(@InjectModel(Manager.name) private managerModel: Model<ManagerDocument>) {}

  /**
   * Login a manager
   *
   * @param {string} email
   * @param {string} password
   * @returns {Promise<Manager & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Manager & Document<any, any, any> & {_id: Types.ObjectId}, Manager & Document<any, any, any> & Required<{_id: U}>> : (Manager & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async login({ email, password }: LoginManagerDto) {
    const manager = await this.managerModel.findOne({ email, password }).exec();

    if (!manager) {
      throw new NotFoundException([
        'No Manager account found with these credentials. Please try again.',
      ]);
    }

    return manager;
  }

  /**
   * Create a new manager
   *
   * @param {CreateManagerDto} createManagerDto
   * @returns {QueryWithHelpers<HydratedDocument<ManagerDocument, {}, {}>, HydratedDocument<ManagerDocument, {}, {}>, {}, ManagerDocument>}
   */
  create(createManagerDto: CreateManagerDto) {
    return this.managerModel.findOneAndUpdate(
      { email: createManagerDto.email },
      { $set: createManagerDto },
      { upsert: true, new: true },
    );
  }

  /**
   * Retrieve all managers
   *
   * @returns {Promise<Array<HydratedDocument<ManagerDocument, {}, {}>>>}
   */
  async findAll() {
    const managers = await this.managerModel.find().exec();

    if (!managers || managers.length === 0) {
      throw new NotFoundException(['No Managers found']);
    }

    return managers;
  }

  /**
   * Retrieve a manager by its id
   *
   * @param {string} id
   * @returns {Promise<Manager & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Manager & Document<any, any, any> & {_id: Types.ObjectId}, Manager & Document<any, any, any> & Required<{_id: U}>> : (Manager & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async findOne({ id }: ManagerIdDto) {
    const manager = await this.managerModel.findById(id).exec();

    if (!manager) {
      throw new NotFoundException(['Manager not found']);
    }

    return manager;
  }

  /**
   * Update a manager by its id
   *
   * @param {string} id
   * @param {UpdateManagerDto} updateManagerDto
   * @returns {Promise<Manager & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Manager & Document<any, any, any> & {_id: Types.ObjectId}, Manager & Document<any, any, any> & Required<{_id: U}>> : (Manager & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async update({ id }: ManagerIdDto, updateManagerDto: UpdateManagerDto) {
    const managers = await this.managerModel.find({ email: updateManagerDto.email }).exec();

    if (managers.length > 0 && managers[0].id !== id) {
      throw new BadRequestException([
        'A Manager with this email already exists. Please try again.',
      ]);
    }

    const updatedManager = await this.managerModel
      .findOneAndUpdate(
        {
          _id: id,
        },
        { $set: updateManagerDto },
        { new: true, upsert: false },
      )
      .exec();

    if (!updatedManager) {
      throw new NotFoundException(['Manager not found']);
    }

    return updatedManager;
  }

  /**
   * Delete a manager by its id
   *
   * @param {string} id
   * @returns {Promise<Manager & Document<any, any, any> extends {_id?: infer U} ? IfAny<U, Manager & Document<any, any, any> & {_id: Types.ObjectId}, Manager & Document<any, any, any> & Required<{_id: U}>> : (Manager & Document<any, any, any> & {_id: Types.ObjectId})>}
   */
  async remove({ id }: ManagerIdDto) {
    const deletedManager = await this.managerModel.findByIdAndDelete(id).exec();

    if (!deletedManager) {
      throw new NotFoundException(['Manager not found']);
    }

    return deletedManager;
  }
}
