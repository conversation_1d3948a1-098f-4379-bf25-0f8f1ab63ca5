# Description

This is the source code repository for the paid version of the EVO PTT REST API

# Installation Instructions for development

1. git clone https://gitlab.com/theofxam/evo-api.git
2. cd evo-api/ or open the newly downloaded folder with Visual Studio Code (preferred)
3. Install the project dependencies by running the command "npm install"
4. Perform any necessary changes (running the code is achieved with the "npm run dev" command and stopping it with Ctrl+C)
5. git add .
6. git commit -am "A new commit message for my change"
7. git push

# Installation Instructions to servers

1. git clone https://gitlab.com/theofxam/evo-api.git
2. Install the project dependencies by running the command "cd evo-api/ && npm install"
3. Run the `pm2 start npm --name "EVO-API" -- run "prod"` command (requires that the initial setup is performed as well as the pm2 Node.JS process manager)"
4. pm2 startup

# Restarting

1. Restarting the whole VPS/dedicated server is achieved with the command "sudo reboot"
2. Restarting the pm2 evo-api process is achieved with the command "pm2 restart all" or
   "pm2 restart (1 or 2 depending on the process id)
3. Restarting all the docker containers is achieved with the command "docker restart $(docker ps -q)"

# Status checking

1. Checking the running processes on the whole VPS/dedicated server is achieved with the "htop" command
2. Checking the status of the evo-api can be achieved with the "pm2 status" and "pm2 logs" command
3. Checking the status of docker can be achieved with the "docker -ps" command
